/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/
#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "sm_measure.h"
#include "sm_measure_mode.h"
#include "lv_canvas.h"
#include "lv_img_buf.h"
#include "gui.h"
#include "twiCW2015.h"

// 分离的组件创建函数
static void create_all_labels(lv_ui *ui)
{
    //Write codes result1_label_1 (日期标签)
//    ui->result1_label_1 = lv_label_create(ui->result1);
//    lv_label_set_text(ui->result1_label_1, "2025/08/28 23:58"); // 先设置默认文本
//    lv_label_set_long_mode(ui->result1_label_1, LV_LABEL_LONG_WRAP);
//    lv_obj_set_pos(ui->result1_label_1, 24, 36);
//    lv_obj_set_size(ui->result1_label_1, 167, 18);
//    
//    // 设置样式
//    lv_obj_set_style_border_width(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_radius(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_color(ui->result1_label_1, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_font(ui->result1_label_1, &lv_font_montserratMedium_18, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_opa(ui->result1_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_letter_space(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_line_space(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_text_align(ui->result1_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_bg_opa(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_pad_top(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_pad_right(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_pad_bottom(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_pad_left(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
//    lv_obj_set_style_shadow_width(ui->result1_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes result1_label_2 (数值标签)
    ui->result1_label_2 = lv_label_create(ui->result1);
    lv_label_set_text(ui->result1_label_2, "      "); // 设置默认空文本
    lv_label_set_long_mode(ui->result1_label_2, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->result1_label_2, 24, 66);
    lv_obj_set_size(ui->result1_label_2, 199, 58);
    
    // 设置样式
    lv_obj_set_style_border_width(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->result1_label_2, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->result1_label_2, &lv_font_montserratMedium_58, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->result1_label_2, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->result1_label_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->result1_label_2, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes result1_label_3 (单位标签)
    ui->result1_label_3 = lv_label_create(ui->result1);
    // 先设置默认单位，后续在configure_display_mode中根据条件修改
    lv_label_set_text(ui->result1_label_3, "mg/dL");
    lv_label_set_long_mode(ui->result1_label_3, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->result1_label_3, 234, 102);
    lv_obj_set_size(ui->result1_label_3, 62, 18);
    
    // 设置样式
    lv_obj_set_style_border_width(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->result1_label_3, lv_color_hex(0xFFFFFF), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->result1_label_3, &lv_font_montserratMedium_17, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->result1_label_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->result1_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->result1_label_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes result1_label_4 (模式标签)
    LV_FONT_DECLARE(lv_font_Chinese_bold);
    ui->result1_label_4 = lv_label_create(ui->result1);
    lv_label_set_text(ui->result1_label_4, "单次"); // 设置默认文本
    lv_label_set_long_mode(ui->result1_label_4, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->result1_label_4, 259, 36);
    lv_obj_set_size(ui->result1_label_4, 40, 18);
    
    // 设置样式
    lv_obj_set_style_border_width(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->result1_label_4, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->result1_label_4, &lv_font_Chinese_bold, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->result1_label_4, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->result1_label_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->result1_label_4, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
	
	
}



static void create_all_images(lv_ui *ui)
{
    //Write codes result1_img_3 蓝色上升/下降条
    ui->result1_img_3 = lv_img_create(ui->result1);
    lv_obj_add_flag(ui->result1_img_3, LV_OBJ_FLAG_CLICKABLE);
    lv_img_set_src(ui->result1_img_3, &_drop_alpha_32x15);
    lv_img_set_pivot(ui->result1_img_3, 50,50);
    lv_img_set_angle(ui->result1_img_3, 0);
    lv_obj_set_pos(ui->result1_img_3, 234, 72);
    lv_obj_set_size(ui->result1_img_3, 32, 15);

    //Write style for result1_img_3
    lv_obj_set_style_img_recolor_opa(ui->result1_img_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_img_opa(ui->result1_img_3, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->result1_img_3, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->result1_img_3, true, LV_PART_MAIN|LV_STATE_DEFAULT);
}

static void create_all_custom_widgets(lv_ui *ui)
{
    // 预先创建所有自定义组件，避免在显示时创建
    battery_create(ui->result1);
    number_square_create(ui->result1, 1);
    bluetooth_image_create(ui->result1);
    
    // 预先创建空的结果线条，避免后续动态创建
    create_empty_result_lines(ui->result1);
}

static void configure_display_mode(lv_ui *ui)
{
    // 根据测量模式配置显示内容
    if(get_measure_mode() == AVERAGE_MODE) {
        // 平均模式：隐藏日期标签，显示平均标签，创建下划线
        lv_obj_add_flag(ui->result1_label_1, LV_OBJ_FLAG_HIDDEN);
        lv_label_set_text(ui->result1_label_4, "平均");
        create_average_date_lines(ui->result1);
//		init_average_measurement_labels(ui->result1);  // 初始化测量结果标签
    } else {
        // 单次模式：显示日期，单次标签
        lv_obj_clear_flag(ui->result1_label_1, LV_OBJ_FLAG_HIDDEN);
        lv_label_set_text(ui->result1_label_4, "单次");
    }
    
    // 配置单位显示
    if(get_measure_unit() == RESULT1_MODE) {
        lv_label_set_text(ui->result1_label_3, "mg/dL");
    } else {
        lv_label_set_text(ui->result1_label_3, "umol/L");
    }
    
    // 配置电池和蓝牙状态
    battery_set_level(get_SOC());
    
    extern bool ble_conn_is_connected(void);
    if(ble_conn_is_connected() != true) {
        bluetooth_image_set_visible(false);
    } else {
        bluetooth_image_set_visible(true);
    }
    
    // 隐藏数值标签
    lv_obj_add_flag(ui->result1_label_2, LV_OBJ_FLAG_HIDDEN);
}


void setup_scr_result1(lv_ui *ui)
{
    // 1. 创建主屏幕容器，初始设为隐藏状态
    ui->result1 = lv_obj_create(NULL);
    lv_obj_set_size(ui->result1, 320, 170);
    lv_obj_set_scrollbar_mode(ui->result1, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_flag(ui->result1, LV_OBJ_FLAG_HIDDEN); // 隐藏整个屏幕
    
    // 2. 暂时禁用自动布局更新
    lv_obj_add_flag(ui->result1, LV_OBJ_FLAG_LAYOUT_1);

    //Write style for result1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->result1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->result1, lv_color_hex(0x030303), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->result1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    // 3. 批量创建所有基础组件
    create_all_labels(ui);
    create_all_images(ui);
    create_all_custom_widgets(ui);
    
    // 4. 在所有组件创建完成后，再进行条件性的显示/隐藏设置
    configure_display_mode(ui);
    
    // 5. 恢复布局并更新
    lv_obj_clear_flag(ui->result1, LV_OBJ_FLAG_LAYOUT_1);
    lv_obj_update_layout(ui->result1);
    
    // 6. 一次性显示整个界面
    lv_obj_clear_flag(ui->result1, LV_OBJ_FLAG_HIDDEN);
    
}



