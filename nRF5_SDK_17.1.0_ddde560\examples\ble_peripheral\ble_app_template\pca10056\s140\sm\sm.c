#include "sm.h"
#include "sm_power_on.h"
#include "sm_power_off.h"
#include "sm_ready.h"
#include "sm_measure.h"
#include "sm_result.h"
#include "sm_history.h"
#include "sm_calibrate.h"
#include "sm_error.h"
#include "sm_uncali.h"
#include "sm_locked.h"
#include "watchdog.h"
#include "gui_guider.h"
#include "sm_measure_mode.h"
#include "sm_measure_unit.h"
#include "sm_lowSOC.h"
#include "sm_baby.h"
#include "sm_ble.h"
#include "sm_charging.h"
#include "key.h"
#include "ST7789.h"


APP_TIMER_DEF(sm_tick_timer_id);        //全局tick
APP_TIMER_DEF(auto_power_off_timer_id); //自动关机计数器
static sm_t m_sm;                       //全局状态机
volatile static uint32_t m_auto_power_off_time_reload;//软件运行时修改的关机时间
volatile static uint32_t m_auto_power_off_countdown;//自动关机计数器
volatile static uint32_t m_power_on_time_ms = 0;   //已开机时间


/* -------------------------------- begin  -------------------------------- */
/**
  * @Name    sleep_mode_enter
  * @brief
  * @param   None
  * @retval
  * <AUTHOR>
  * @Data    2025-07-21
  * 1. ...
  * <Function>:Shutdown
 **/
/* -------------------------------- end -------------------------------- */
static void sleep_mode_enter(void)
{
    ret_code_t err_code;

    //关闭显示屏
    nrf_gpio_pin_clear(GPIO_LCD_BK); // 关闭背光
    close_LCD(); // 关屏

    //唤醒按键设置
    nrf_gpio_cfg_sense_input(KEY_USER_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);

    //配置CHRG_DET作为唤醒源 - 插入充电器时唤醒
    nrf_gpio_cfg_sense_input(LGS4056_CHRG_PIN, NRF_GPIO_PIN_PULLUP, NRF_GPIO_PIN_SENSE_LOW);

    // Go to system-off mode (this function will not return; wakeup will cause a reset).
    err_code = sd_power_system_off();
    APP_ERROR_CHECK(err_code);
}

//重载自动关机定时器
void system_auto_power_off_timer_reload(void)
{
    m_auto_power_off_countdown = m_auto_power_off_time_reload;
}


//自动关机定时器回调
static void auto_power_off_timeout_handler(void * p_context)
{
    if (m_auto_power_off_countdown > 0) {
        m_auto_power_off_countdown--;
        if (m_auto_power_off_countdown == 0) {
            sleep_mode_enter();//自动关机
        }
    }
}


//获取全局状态机
sm_t sm_get(void)
{
    return m_sm;
}


void sm_jump(sm_t new_sm, uint32_t para)
{
    m_sm = new_sm;
    switch (m_sm) {
    case SM_POWER_ON:
        NRF_LOG_INFO("SM_POWER_ON");
        sm_power_on_init();
        break;

    case SM_READY:
        NRF_LOG_INFO("SM_READY");
        sm_ready_init();
        break;

    case SM_MEASURE:
        NRF_LOG_INFO("SM_MEASURE");
        sm_measure_init();
        break;

    case SM_MEASURE_MODE:
        NRF_LOG_INFO("SM_MEASURE_MODE");
        sm_measure_mode_init();
        break;

    case SM_BABY:
        NRF_LOG_INFO("SM_BABY");
        sm_baby_init();
        break;

    case SM_HISTORY:
        NRF_LOG_INFO("SM_HISTORY");
        sm_history_init();
        break;

    case SM_CALIBRATE:
        NRF_LOG_INFO("SM_CALIBRATE");
        //sm_calibrate_init();
        break;

    case SM_POWER_OFF:
        NRF_LOG_INFO("SM_POWER_OFF");
        sm_power_off_init();
        break;

    case SM_LOCK:
        NRF_LOG_INFO("SM_LOCK");
        //sm_lock_init();
        break;

    case SM_ERROR:
        NRF_LOG_INFO("SM_ERROR");
        //sm_error_init();
        break;
    case SM_LOWSOC:
        NRF_LOG_INFO("SM_LOWSOC");
        sm_lowSOC_init();
        break;
    case SM_BLE:
        sm_ble_init();
        break;
    case SM_CHARG:
        sm_charging_init();
    default:
        NRF_LOG_WARNING("Unknown state: %d", m_sm);
        break;
    }
}




// 每1ms定时调用
static void sm_tick_timeout_handler(void * p_context)
{
    m_power_on_time_ms++;
    key_system_recovery();  // 按键系统恢复检查

    switch (m_sm) {
    case SM_POWER_ON:
        sm_power_on_tick(); // 如需上电动画或延迟
        break;
    case SM_READY:
        sm_ready_tick();
        break;
    case SM_MEASURE:
        sm_measure_tick();
        break;
    case SM_MEASURE_MODE:
        sm_measure_mode_tick();
        break;
    case SM_BABY:
        sm_baby_tick();
        break;
    case SM_HISTORY:
        sm_history_tick();
        break;
    case SM_CALIBRATE:
        sm_calibrate_tick();
        break;
    case SM_POWER_OFF:
        sm_power_off_tick();
        break;
    case SM_LOCK:
        //sm_lock_tick();
        break;
    case SM_ERROR:
        sm_error_tick();
        break;
    case SM_LOWSOC:
        sm_lowSOC_tick();
        break;
    case SM_BLE:
        sm_ble_tick();
        break;
    case SM_CHARG:
        sm_charging_tick();
    default:
        break;
    }
}


// UI 事件分发
void sm_event(ui_evt_t ui_evt)
{
    switch (m_sm) {
    case SM_POWER_ON:
        sm_power_on_event(ui_evt);
        break;
    case SM_READY:
        sm_ready_event(ui_evt);
        break;
    case SM_MEASURE:
        sm_measure_event(ui_evt);
        break;
    case SM_MEASURE_MODE:
        sm_measure_mode_event(ui_evt);
        break;
    case SM_BABY:
        sm_baby_event(ui_evt);
        break;
    case SM_HISTORY:
        sm_history_event(ui_evt);
        break;
    case SM_CALIBRATE:
        sm_calibrate_event(ui_evt);
        break;
    case SM_POWER_OFF:
        sm_power_off_event(ui_evt);
        break;
    case SM_LOCK:
        //sm_lock_event(ui_evt);
        break;
    case SM_ERROR:
        sm_error_event(ui_evt);
        break;
    case SM_LOWSOC:
        sm_lowSOC_event(ui_evt);
        break;
    case SM_BLE:
        sm_ble_event(ui_evt);
        break;
    case SM_CHARG:
        sm_charging_event(ui_evt);
    default:
        break;
    }
}



//状态机初始化
void sm1_init(void)
{
    ret_code_t ret_code;

    m_power_on_time_ms = 0;

    //全局tick定时器
    ret_code = app_timer_create(&sm_tick_timer_id, APP_TIMER_MODE_REPEATED, sm_tick_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(sm_tick_timer_id, APP_TIMER_TICKS(1), NULL);
    APP_ERROR_CHECK(ret_code);

    //自动关机定时器
    m_auto_power_off_time_reload = AUTO_POWER_OFF_SECOND;
    system_auto_power_off_timer_reload();
    ret_code = app_timer_create(&auto_power_off_timer_id, APP_TIMER_MODE_REPEATED, auto_power_off_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(auto_power_off_timer_id, APP_TIMER_TICKS(1000), NULL);
    APP_ERROR_CHECK(ret_code);
    //进入界面
    sm_jump(SM_POWER_ON, 0);
}

//运行时设置自动关机时间
void sm_set_auto_power_off_time(uint32_t second)
{
    m_auto_power_off_time_reload = second;
    system_auto_power_off_timer_reload();

}
//开启自动关机定时器
void sm_open_timer(void)
{
    ret_code_t ret_code = app_timer_start(auto_power_off_timer_id, APP_TIMER_TICKS(1000), NULL);
    APP_ERROR_CHECK(ret_code);
}

//关闭自动关机定时器
void sm_close_timer(void)
{
    ret_code_t err_code = app_timer_stop(auto_power_off_timer_id);
    APP_ERROR_CHECK(err_code);
}

//获取已开机时间
uint32_t sm_get_power_on_time(void)
{
    return m_power_on_time_ms;
}
